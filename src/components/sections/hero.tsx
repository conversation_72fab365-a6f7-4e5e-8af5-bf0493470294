"use client";

import React from "react";
import { <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { CreateAccountButton } from "@/components/create-free-account-btn";
import Image from "next/image";

export const HeroSection: React.FC = () => {
	return (
		<section className="min-h-screen flex items-center pt-16 bg-gradient-to-br from-slate-900 to-slate-800 relative overflow-hidden">
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(34,197,94,0.1)_0%,transparent_50%),radial-gradient(circle_at_75%_75%,rgba(34,197,94,0.05)_0%,transparent_50%)] pointer-events-none" />

			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="grid lg:grid-cols-2 gap-12 items-center">
					<div className="animate-[fadeInUp_0.8s_ease-out] mt-6 lg:mt-0">
						<h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight mb-6 mt-12 md:mt-0">
							<span className="bg-gradient-to-r from-slate-50 to-green-500 bg-clip-text text-transparent">
								Stop Running Your Business.
							</span>
							<br />
							<span className="text-slate-300">
								Let AI Run It.
							</span>
						</h1>

						<p className="text-xl text-slate-400 mb-8 leading-relaxed">
							AI agents work 24/7 to cut costs, find better deals,
							and scale your sales while you focus on growth.
						</p>

						<div className="flex sm:flex-row gap-4 mb-8">
							<CreateAccountButton
								size="lg"
								className="text-lg px-8"
							/>

							<Button
								variant="outline"
								size="lg"
								className="text-lg px-8 border-slate-700 bg-transparent text-slate-50 hover:border-green-500 hover:text-green-500">
								Watch Demo
							</Button>
						</div>

						<div className="space-y-3">
							<div className="flex flex-wrap gap-6">
								<div className="flex items-center gap-2 text-sm text-slate-400">
									<Star className="w-4 h-4 text-green-500" />
									No credit card required
								</div>
								<div className="flex items-center gap-2 text-sm text-slate-400">
									<Clock className="w-4 h-4 text-green-500" />
									Setup in 5 minutes
								</div>
							</div>
						</div>
					</div>

					<div className="animate-[fadeInUp_0.8s_ease-out] [animation-delay:0.2s]">
						<Card className="p-8 text-center relative overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300">
							<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />

							<Image
								src="/logo.png"
								alt="Screenshot of Clickbuy Profit Calculator extension"
								width={40}
								height={40}
								className="w-full h-auto scale-60"
								priority
							/>
							<h3 className="text-2xl font-semibold mb-4 text-slate-50 -mt-18">
								AI-First E-commerce Platform
							</h3>
							<p className="text-slate-400 mb-6">
								Your promotion to CEO starts here. Let AI handle
								the operations.
							</p>

							{/* Stats */}
							<div className="grid grid-cols-3 gap-4">
								<div className="text-center">
									<div className="text-2xl font-bold text-green-500">
										80%
									</div>
									<div className="text-xs text-slate-400">
										Cost Cut
									</div>
								</div>
								<div className="text-center">
									<div className="text-2xl font-bold text-green-500">
										24/7
									</div>
									<div className="text-xs text-slate-400">
										AI Works
									</div>
								</div>
								<div className="text-center">
									<div className="text-2xl font-bold text-green-500">
										300%
									</div>
									<div className="text-xs text-slate-400">
										Growth
									</div>
								</div>
							</div>
						</Card>
					</div>
				</div>
			</div>
		</section>
	);
};
