"use client"
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from "react";
import Image from "next/image";
import {
	Search,
	Zap,
	TrendingUp,
	AlertTriangle,
	CheckCircle,
	Star,
	Package,
	DollarSign,
	Eye,
	Target,
	BarChart3,
	Users,
	Activity,
	RefreshCw,
	ExternalLink,
	Bookmark,
	Download,
	Loader2,
} from "lucide-react";
import { ProductAnalysis } from "@/types/extension";
import Button from "@/components/Button";
import Overview from "./product-overview";
import { useProductContext } from "@/hooks/use-product-analysis";
import { Input } from "../ui/input";
import AccordionData from "./accordion-data";


interface ProductAnalyzerProps {
	onAnalyze: (asin: string) => Promise<ProductAnalysis>;
	recentAnalyses: ProductAnalysis[];
	isDarkMode?: boolean;
	isLoading?: boolean;
}

// Get default analysis from recent analyses or create fallback
const getDefaultAnalysis = (
	recentAnalyses: ProductAnalysis[]
): ProductAnalysis | null => {
	// Use the gaming headset from recent analyses if available
	const gamingHeadset = recentAnalyses.find(
		(analysis) => analysis.asin === "B08XYZ789"
	);
	if (gamingHeadset) {
		return gamingHeadset;
	}

	// Use the first profitable analysis if available
	const profitableAnalysis = recentAnalyses.find(
		(analysis) =>
			analysis.profitability.profit > 0 &&
			analysis.aiInsights.recommendation === "Buy"
	);
	if (profitableAnalysis) {
		return profitableAnalysis;
	}

	// Use the first analysis if available
	if (recentAnalyses.length > 0) {
		return recentAnalyses[0];
	}

	// Fallback to null if no recent analyses
	return null;
};

const ProductAnalyzer: React.FC<ProductAnalyzerProps> = ({
	onAnalyze,
	recentAnalyses,
	isDarkMode = true,
}) => {
	const {asin, setAsin, productData, keepaLoading} = useProductContext();
	const [analyzing, setAnalyzing] = useState(false);
	const [currentAnalysis, setCurrentAnalysis] =
		useState<ProductAnalysis | null>(getDefaultAnalysis(recentAnalyses));
	const [activeView, setActiveView] = useState<
		"overview" | "trends" | "competitors"
	>("overview");

	const handleAnalyze = async () => {
		if (!asin || analyzing) return;

		setAnalyzing(true);
		try {
			const result = await onAnalyze(asin.trim());
			setCurrentAnalysis(result);
		} catch (error) {
			console.error("Analysis failed:", error);
		} finally {
			setAnalyzing(false);
		}
	};

	const getRecommendationColor = (recommendation: string) => {
		switch (recommendation) {
			case "Buy":
				return "text-green-500 bg-green-500/10 border-green-500/20";
			case "Avoid":
				return "text-red-500 bg-red-500/10 border-red-500/20";
			case "Research More":
				return "text-yellow-500 bg-yellow-500/10 border-yellow-500/20";
			default:
				return "text-gray-500 bg-gray-500/10 border-gray-500/20";
		}
	};

	const getRiskColor = (risk: string) => {
		switch (risk) {
			case "Low":
				return "text-green-500";
			case "Medium":
				return "text-yellow-500";
			case "High":
				return "text-red-500";
			default:
				return "text-gray-500";
		}
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-GB", {
			style: "currency",
			currency: "GBP",
			minimumFractionDigits: 3,
		}).format(amount);
	};

	const formatPercentage = (value: number) => {
		return `${value > 0 ? "+" : ""}${value.toFixed(1)}%`;
	};

	console.log('productData ===>', productData)
	return (
		<div
			className={`${isDarkMode ? "bg-gray-900" : "bg-white"} border ${
				isDarkMode ? "border-gray-700" : "border-gray-200"
			} rounded-lg overflow-hidden`}>
			{/* Header */}
			<div className="p-6 border-b border-gray-700">
				<div className="flex items-center justify-between mb-4">
					<div>
						<h3
							className={`text-xl font-bold ${
								isDarkMode ? "text-white" : "text-gray-900"
							}`}>
							Product Analyzer
						</h3>
						<p
							className={`text-sm mt-1 ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							AI-powered Amazon product analysis with
							profitability insights
						</p>
					</div>

				</div>

				{/* ASIN Input */}
				<div className="flex gap-3">
					<div className="flex-1">
						<Input
							placeholder="Enter ASIN (e.g., B09SVGB8GG)"
							value={asin ?? ""}
							// isDarkMode={isDarkMode}
							className={`${isDarkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-black'}`}
							onChange={(e) =>
								setAsin(
									(
										e.target as HTMLInputElement
									).value.toUpperCase()
								)
							}
						/>
					</div>
					<Button
						variant="primary"
						onClick={handleAnalyze}
						disabled={analyzing}
						className={`flex items-center gap-2 px-6 cursor-pointer ${isDarkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-700 hover:bg-gray-600'}`}>
						{analyzing ? (
							<>
								<RefreshCw size={16} className="animate-spin" />
								Analyzing...
							</>
						) : (
							<>
								<Zap size={16} />
								Analyze
							</>
						)}
					</Button>
				</div>

				{keepaLoading && (
					<div className="mt-4 h-[78vh] flex flex-col items-center justify-center border rounded-lg">
						<Loader2 className="w-8 h-8 text-green-500 animate-spin" />
						<h3 className={`text-lg font-medium mb-2 ${isDarkMode ? "text-white" : "text-gray-900"}`}>
							Loading Product Data
						</h3>
						<p className={`text-sm text-center max-w-md ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
							Fetching product information and analyzing market data...
						</p>
					</div>
				)}

				{
					(!productData && !keepaLoading) && (
						<div className="mt-4 h-[78vh] flex flex-col items-center justify-center border rounded-lg">
							<Search className={`w-12 h-12 mb-4 ${isDarkMode ? "text-gray-600" : "text-gray-400"}`} />
							<h3 className={`text-lg font-medium mb-2 ${isDarkMode ? "text-white" : "text-gray-900"}`}>
								Product Analysis
							</h3>
							<p className={`text-sm text-center max-w-md ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
								Enter an Amazon ASIN above to analyze product profitability, market trends, and get AI-powered insights.
							</p>
							<div className="mt-6 flex items-center gap-2 text-sm text-gray-500">
								<AlertTriangle size={14} />
								<span>Example ASIN: B09SVGB8GG</span>
							</div>
						</div>
					)
				}
				{
					productData && (
						<div className="mt-4">
							<div className={`relative mt-8 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'} border px-2 py-1 flex items-center justify-center`}>
								<span className={`text-sm font-extrabold tracking-wide ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
									Overview
								</span>
			
								<div className="absolute right-2 flex items-center gap-2">
									<Button
										className={`${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'} p-0.5 rounded h-fit`}
										title="Refresh Analysis"
									>
										<RefreshCw size={14} className={isDarkMode ? 'text-gray-300' : 'text-gray-700'} />
									</Button>
								</div>
							</div>
							<Overview isDarkMode={isDarkMode} />
							<AccordionData isDarkMode={isDarkMode} />
						</div>
					)
				}
			</div>

		
		</div>
	);
};


export default ProductAnalyzer;
