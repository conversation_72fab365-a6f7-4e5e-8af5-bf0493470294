import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogOverlay,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, Star, MessageSquare } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Textarea } from "@/components/ui/textarea";
import { $http } from "@/lib/api/http";
import { toast } from "sonner";
import { getAccessToken } from "@/lib";

const formSchema = z.object({
  rating: z.number().min(1, "Please select a rating").max(5),
  message: z.string().min(10, "Feedback must be at least 10 characters"),
});

interface FeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  isDarkMode?: boolean;
}

const FeedbackModal: React.FC<FeedbackModalProps> = ({
  isOpen,
  onClose,
  isDarkMode = true,
}) => {
  const [hoveredRating, setHoveredRating] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      rating: 0,
      message: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
      const accessToken = typeof window !== 'undefined' ? getAccessToken() : null;

    try {
      await $http.post(
        "extension-backend/api/v1/support",
        values,
        {
          headers: {
            accept: "application/json",
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );
      onClose();
      setIsSubmitting(false);
      toast.success("Feedback submitted successfully!");
    } catch (error) {
      console.error("Feedback submission error:", error);
      toast.error("Failed to submit feedback. Please try again.");
      setIsSubmitting(false);
    }
    
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className={`backdrop-blur-sm ${isDarkMode ? 'bg-black/70' : 'bg-white/70'}`}>
        <DialogContent
          className={`sm:max-w-2xl rounded-2xl shadow-2xl border-0 p-0 overflow-hidden ${
            isDarkMode
              ? 'bg-gray-800 border-gray-700'
              : 'bg-white border-gray-200'
          }`}
          showCloseButton={false}
        >
          <div className="relative">
            <DialogHeader className={`px-6 pt-6 pb-4 border-b ${
              isDarkMode ? 'border-gray-700' : 'border-gray-100'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full flex items-center justify-center bg-brand-primary/20">
                    <MessageSquare size={20} className="text-brand-primary" />
                  </div>
                  <div>
                    <DialogTitle className={`text-lg font-semibold ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      Share Your Feedback
                    </DialogTitle>
                    <p className={`text-sm ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-600'
                    }`}>
                      Help us improve your experience
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className={`rounded-md transition-colors cursor-pointer ${
                    isDarkMode
                      ? 'hover:!bg-brand-primary text-gray-400 hover:text-gray-200'
                      : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <X size={20} />
                </Button>
              </div>
            </DialogHeader>

            <div className="p-6">
              <form 
                  onSubmit={form.handleSubmit(onSubmit)} 
                  className="space-y-6"
              >
                <div>
                  <label className={`text-sm font-medium ${
                    isDarkMode ? 'text-gray-200' : 'text-gray-700'
                  }`}>
                    How would you rate your experience?
                  </label>
                  <div className="flex gap-1 mt-2">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        type="button"
                        onClick={() => form.setValue('rating', rating)}
                        onMouseEnter={() => setHoveredRating(rating)}
                        onMouseLeave={() => setHoveredRating(0)}
                        className="p-1 rounded-md transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-brand-primary/50"
                      >
                        {rating <= (hoveredRating || form.watch('rating')) ? (
                          <Star className="w-8 h-8 fill-current text-brand-primary" />
                        ) : (
                          <Star className={`w-8 h-8 ${
                            isDarkMode ? 'text-gray-600' : 'text-gray-300'
                          } hover:text-brand-primary/50 transition-colors`} />
                        )}
                      </button>
                    ))}
                  </div>
                  {form.watch('rating') > 0 && (
                    <p className={`text-xs mt-2 ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      {form.watch('rating') === 1 && "Poor - We'll do better"}
                      {form.watch('rating') === 2 && "Fair - Room for improvement"}
                      {form.watch('rating') === 3 && "Good - Meeting expectations"}
                      {form.watch('rating') === 4 && "Very Good - Exceeding expectations"}
                      {form.watch('rating') === 5 && "Excellent - Outstanding experience!"}
                    </p>
                  )}
                  {(form.formState.errors.rating && form.watch('rating') === 0) && (
                    <p className="text-red-500 text-sm mt-1">{form.formState.errors.rating.message}</p>
                  )}
                </div>

                <div className="mt-6">
                  <label className={`text-sm font-medium ${
                    isDarkMode ? 'text-gray-200' : 'text-gray-700'
                  }`}>
                    Tell us more about your experience
                  </label>
                  <Textarea
                    {...form.register('message')}
                    placeholder="Share your thoughts, suggestions, or any issues you encountered..."
                    className={`min-h-[120px] resize-none transition-all duration-200 mt-2 ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400 focus:border-brand-primary focus:ring-brand-primary/20'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-brand-primary focus:ring-brand-primary/20'
                    }`}
                  />
                  {form.formState.errors.message && (
                    <p className="text-red-500 text-sm mt-1">{form.formState.errors.message.message}</p>
                  )}
                </div>

                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onClose}
                    className={`cursor-pointer ${
                      isDarkMode
                        ? 'border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-gray-200'
                        : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-brand-primary text-white cursor-pointer shadow-brand transition-all duration-200"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Submitting...
                      </div>
                    ) : (   
                      "Submit Feedback"
                    )} 
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </DialogContent>
      </DialogOverlay>
    </Dialog>
  );
};

export default FeedbackModal;
