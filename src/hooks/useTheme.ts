// src/hooks/useTheme.ts
import { useState, useEffect } from "react";

export type Theme = "dark" | "light";

export function useTheme() {
	const [theme, setTheme] = useState<Theme>("dark");
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);

		// Only access localStorage after mounting
		try {
			const savedTheme = localStorage.getItem("theme") as Theme;
			if (
				savedTheme &&
				(savedTheme === "dark" || savedTheme === "light")
			) {
				setTheme(savedTheme);
				applyTheme(savedTheme);
			} else {
				applyTheme("dark");
			}
		} catch {
			// Fallback if localStorage is not available
			applyTheme("dark");
		}
	}, []);

	const applyTheme = (newTheme: Theme) => {
		if (typeof document === "undefined") return;

		const root = document.documentElement;

		if (newTheme === "dark") {
			root.classList.add("dark");
			root.classList.remove("light");
		} else {
			root.classList.add("light");
			root.classList.remove("dark");
		}
	};

	const toggleTheme = () => {
		if (!mounted) return;

		const newTheme: Theme = theme === "dark" ? "light" : "dark";
		setTheme(newTheme);
		applyTheme(newTheme);

		try {
			localStorage.setItem("theme", newTheme);
		} catch (error) {
			// Handle localStorage errors gracefully
			console.warn("Failed to save theme to localStorage:", error);
		}
	};

	// Always return the same values during server-side rendering
	return {
		theme: mounted ? theme : "dark",
		isDarkMode: mounted ? theme === "dark" : true,
		toggleTheme,
		mounted,
	};
}
