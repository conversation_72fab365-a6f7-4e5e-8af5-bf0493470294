// src/app/layout.tsx
import type { Metadata } from "next";
import { Toaster } from "@/components/ui/sonner";
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";

// FontAwesome imports
import { config } from "@fortawesome/fontawesome-svg-core";
import "@fortawesome/fontawesome-svg-core/styles.css";
import { AppProviders } from "@/components/provider";
import { ProductProvider } from "@/hooks/use-product-analysis";
config.autoAddCss = false;

const geistSans = Geist({
	variable: "--font-geist-sans",
	subsets: ["latin"],
});

const geistMono = Geist_Mono({
	variable: "--font-geist-mono",
	subsets: ["latin"],
});

export const metadata: Metadata = {
	title: "ClickBuy",
	description: "Welcome to clickbuy agentic e-commerce platform (aep)",
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<body
				className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-slate-900`}>
				<AppProviders>
					<Toaster richColors />
					<ProductProvider>
					{children}
					</ProductProvider>
				</AppProviders>
			</body>
		</html>
	);
}
